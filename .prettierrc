{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "semi": true, "experimentalTernaries": false, "singleQuote": true, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "printWidth": 80, "requirePragma": false, "tabWidth": 2, "useTabs": true, "embeddedLanguageFormatting": "auto", "cursorOffset": -1, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["<THIRD_PARTY_MODULES>", "^@/(.*)$", "^./(.*)$"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"]}