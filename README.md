# Rose Shop

#### 1. **Distribute Vouchers** (`src/app/seller/distribute-vouchers/page.tsx`)
- Filter vouchers by status (all / available / distributed)
- Select a voucher and assign to a user
- Displays confirmation messages upon successful distribution
- Allows manual voucher entry

Admin Pages

#### 2. **Admin Dashboard** (`src/app/admin/dashboard/page.tsx`)
- Overview of system metrics
- Placeholder for charts, user activities, and quick stats

#### 3. **User Management** (`src/app/users/user-management/page.tsx`)
- List all registered users
- Placeholder for user roles, actions, and status
- Foundation for editing, banning, or promoting users

---

1. Clone the repository

2. Install dependencies
npm install

3. Run in development mode
npm run dev

4. Open in browser
http://localhost:3000
-URL: `http://localhost:3000/seller/distribute-vouchers`
-URL: `http://localhost:3000/admin/dashboard`
-URL:`http://localhost:3000/admin/users/user-management`
